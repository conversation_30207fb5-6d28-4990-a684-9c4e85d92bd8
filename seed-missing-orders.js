import dotenv from 'dotenv';
import { neon } from '@neondatabase/serverless';

// Load environment variables
dotenv.config();

const DATABASE_URL = process.env.VITE_DATABASE_URL;
const sql = neon(DATABASE_URL);

console.log('🚀 Seeding missing orders data...');

const demoOrders = [
  {
    orderReferenceNumber: 'IS-1703845200000',
    orderOnboardingDate: '2024-06-20',
    client: 'Acme Corporation',
    typeOfWork: 'Software Development',
    dateOfWorkCompletionExpected: '2024-07-20',
    totalInvoiceValue: 245000,
    totalValueGstGovtFees: 44100,
    dateOfPaymentExpected: '2024-07-25',
    dateOfOnboardingVendor: '2024-06-21',
    vendorName: 'TechCorp Solutions',
    status: 'Completed'
  },
  {
    orderReferenceNumber: 'IS-1703845260000',
    orderOnboardingDate: '2024-06-25',
    client: 'Global Tech Inc',
    typeOfWork: 'Digital Marketing',
    dateOfWorkCompletionExpected: '2024-08-25',
    totalInvoiceValue: 35000,
    totalValueGstGovtFees: 6300,
    dateOfPaymentExpected: '2024-08-30',
    dateOfOnboardingVendor: '2024-06-27',
    vendorName: 'Marketing Pro Agency',
    status: 'In Progress'
  },
  {
    orderReferenceNumber: 'IS-1703845320000',
    orderOnboardingDate: '2024-07-01',
    client: 'StartUp Solutions',
    typeOfWork: 'Graphic Design',
    dateOfWorkCompletionExpected: '2024-08-01',
    totalInvoiceValue: 25000,
    totalValueGstGovtFees: 4500,
    dateOfPaymentExpected: '2024-08-05',
    dateOfOnboardingVendor: '2024-07-03',
    vendorName: 'Creative Design Studio',
    status: 'Pending'
  },
  {
    orderReferenceNumber: 'IS-1703845380000',
    orderOnboardingDate: '2024-07-10',
    client: 'Innovative Minds Ltd',
    typeOfWork: 'Content Writing',
    dateOfWorkCompletionExpected: '2024-08-10',
    totalInvoiceValue: 18000,
    totalValueGstGovtFees: 3240,
    dateOfPaymentExpected: '2024-08-15',
    dateOfOnboardingVendor: '2024-07-12',
    vendorName: 'Legal Advisors LLP',
    status: 'In Progress'
  },
  {
    orderReferenceNumber: 'IS-1703845440000',
    orderOnboardingDate: '2024-07-15',
    client: 'Enterprise Corp',
    typeOfWork: 'Data Analysis',
    dateOfWorkCompletionExpected: '2024-09-15',
    totalInvoiceValue: 60000,
    totalValueGstGovtFees: 10800,
    dateOfPaymentExpected: '2024-09-20',
    dateOfOnboardingVendor: '2024-07-17',
    vendorName: 'Global Supplies Inc',
    status: 'Completed'
  }
];

// Missing clients data
const missingClients = [
  {
    company_name: 'Global Tech Inc',
    company_type: 'DPIIT',
    onboarding_date: '2024-02-15',
    emails: ['<EMAIL>'],
    phones: ['+91-9876543220'],
    address: 'Innovation Park, Electronic City, Bangalore',
    country: 'India',
    state: 'Karnataka',
    city: 'Bangalore',
    dpiit_registered: true,
    dpiit_number: 'DPIIT12345',
    status: 'Active'
  },
  {
    company_name: 'StartUp Solutions',
    company_type: 'MSME',
    onboarding_date: '2024-03-20',
    emails: ['<EMAIL>'],
    phones: ['+91-9876543230'],
    address: 'Business District, Gurgaon',
    country: 'India',
    state: 'Haryana',
    city: 'Gurgaon',
    dpiit_registered: false,
    dpiit_number: '',
    status: 'Active'
  },
  {
    company_name: 'Innovative Minds Ltd',
    company_type: 'Small Entity',
    onboarding_date: '2024-04-25',
    emails: ['<EMAIL>'],
    phones: ['+91-9876543240'],
    address: 'Tech Tower, Hyderabad',
    country: 'India',
    state: 'Telangana',
    city: 'Hyderabad',
    dpiit_registered: false,
    dpiit_number: '',
    status: 'Active'
  }
];

async function seedMissingData() {
  try {
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful');

    // Seed missing clients first
    console.log('\n👥 Seeding missing clients...');
    for (const client of missingClients) {
      try {
        await sql`
          INSERT INTO clients (
            company_name, company_type, onboarding_date, emails, phones,
            address, country, state, city, dpiit_registered, dpiit_number, status
          ) VALUES (
            ${client.company_name}, ${client.company_type}, ${client.onboarding_date},
            ${JSON.stringify(client.emails)}, ${JSON.stringify(client.phones)},
            ${client.address}, ${client.country}, ${client.state}, ${client.city},
            ${client.dpiit_registered}, ${client.dpiit_number}, ${client.status}
          )
        `;
        console.log(`✅ Seeded client: ${client.company_name}`);
      } catch (error) {
        if (error.message.includes('duplicate key')) {
          console.log(`⚠️ Client ${client.company_name} already exists`);
        } else {
          console.log(`⚠️ Client ${client.company_name} error:`, error.message);
        }
      }
    }

    // Seed orders
    console.log('\n📋 Seeding demo orders...');
    for (const order of demoOrders) {
      try {
        await sql`
          INSERT INTO orders (
            orderReferenceNumber, orderOnboardingDate, client, typeOfWork,
            dateOfWorkCompletionExpected, totalInvoiceValue, totalValueGstGovtFees,
            dateOfPaymentExpected, dateOfOnboardingVendor, vendorName, status
          ) VALUES (
            ${order.orderReferenceNumber}, ${order.orderOnboardingDate}, ${order.client},
            ${order.typeOfWork}, ${order.dateOfWorkCompletionExpected},
            ${order.totalInvoiceValue}, ${order.totalValueGstGovtFees},
            ${order.dateOfPaymentExpected}, ${order.dateOfOnboardingVendor},
            ${order.vendorName}, ${order.status}
          )
        `;
        console.log(`✅ Seeded order: ${order.orderReferenceNumber}`);
      } catch (error) {
        if (error.message.includes('duplicate key')) {
          console.log(`⚠️ Order ${order.orderReferenceNumber} already exists`);
        } else {
          console.log(`⚠️ Order ${order.orderReferenceNumber} error:`, error.message);
        }
      }
    }

    // Final verification
    console.log('\n📊 Final verification...');
    const vendorCount = await sql`SELECT COUNT(*) as count FROM vendors WHERE company_name IN ('TechCorp Solutions', 'Global Supplies Inc', 'Creative Design Studio', 'Marketing Pro Agency', 'Legal Advisors LLP')`;
    const clientCount = await sql`SELECT COUNT(*) as count FROM clients WHERE company_name IN ('Acme Corporation', 'Global Tech Inc', 'StartUp Solutions', 'Innovative Minds Ltd', 'Enterprise Corp')`;
    const orderCount = await sql`SELECT COUNT(*) as count FROM orders WHERE orderReferenceNumber LIKE 'IS-%'`;
    const subAdminCount = await sql`SELECT COUNT(*) as count FROM sub_admins WHERE email LIKE '%@innoventory.com'`;
    const workTypeCount = await sql`SELECT COUNT(*) as count FROM type_of_work WHERE name IN ('Software Development', 'Digital Marketing', 'Graphic Design', 'Content Writing', 'Data Analysis')`;

    console.log('📈 FINAL DATA COUNTS:');
    console.log(`   - Demo Vendors: ${vendorCount[0].count}/5 ${vendorCount[0].count === 5 ? '✅' : '❌'}`);
    console.log(`   - Demo Clients: ${clientCount[0].count}/5 ${clientCount[0].count >= 5 ? '✅' : '❌'}`);
    console.log(`   - Demo Orders: ${orderCount[0].count}/5 ${orderCount[0].count >= 5 ? '✅' : '❌'}`);
    console.log(`   - Demo Sub-admins: ${subAdminCount[0].count}/5 ${subAdminCount[0].count === 5 ? '✅' : '❌'}`);
    console.log(`   - Demo Type of work: ${workTypeCount[0].count}/5 ${workTypeCount[0].count === 5 ? '✅' : '❌'}`);

    if (vendorCount[0].count === 5 && clientCount[0].count >= 5 && orderCount[0].count >= 5 && subAdminCount[0].count === 5 && workTypeCount[0].count === 5) {
      console.log('\n🎉 SUCCESS! ALL DEMO DATA IS NOW SAVED IN THE DATABASE!');
      console.log('✅ Your admin panel is fully connected to the database with real data.');
      console.log('🚀 All data displayed in the admin panel pages is now saved and persistent.');
    } else {
      console.log('\n⚠️ Some data is still missing. Please check the errors above.');
    }

  } catch (error) {
    console.error('❌ Error seeding data:', error.message);
    process.exit(1);
  }
}

seedMissingData();
