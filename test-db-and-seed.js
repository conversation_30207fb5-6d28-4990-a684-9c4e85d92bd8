import dotenv from 'dotenv';
import { neon } from '@neondatabase/serverless';

// Load environment variables
dotenv.config();

const DATABASE_URL = process.env.VITE_DATABASE_URL;

console.log('🚀 Testing database connection and seeding demo data...');
console.log('📍 Database URL:', DATABASE_URL ? DATABASE_URL.replace(/:[^:@]*@/, ':****@') : 'NOT SET');

if (!DATABASE_URL) {
  console.error('❌ VITE_DATABASE_URL is not set in environment variables');
  process.exit(1);
}

const sql = neon(DATABASE_URL);

async function testAndSeed() {
  try {
    // Test connection
    console.log('🔄 Testing database connection...');
    const result = await sql`SELECT 1 as test, version() as db_version`;
    console.log('✅ Database connection successful');
    console.log('📊 Database info:', result[0]);

    // Initialize schema
    console.log('\n🔄 Initializing database schema...');
    
    // Create vendors table
    await sql`
      CREATE TABLE IF NOT EXISTS vendors (
        id SERIAL PRIMARY KEY,
        company_name VARCHAR(255) NOT NULL UNIQUE,
        company_type VARCHAR(100),
        onboarding_date DATE,
        emails JSONB DEFAULT '[]',
        phones JSONB DEFAULT '[]',
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        username VARCHAR(255),
        gst_number VARCHAR(50),
        description TEXT,
        services JSONB DEFAULT '[]',
        website VARCHAR(255),
        type_of_work VARCHAR(255),
        status VARCHAR(50) DEFAULT 'Active',
        rating DECIMAL(3,2) DEFAULT 0.00,
        total_orders INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create clients table
    await sql`
      CREATE TABLE IF NOT EXISTS clients (
        id SERIAL PRIMARY KEY,
        company_name VARCHAR(255) NOT NULL UNIQUE,
        company_type VARCHAR(100),
        onboarding_date DATE,
        emails JSONB DEFAULT '[]',
        phones JSONB DEFAULT '[]',
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        dpiit_registered BOOLEAN DEFAULT FALSE,
        dpiit_number VARCHAR(100),
        status VARCHAR(50) DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create sub_admins table
    await sql`
      CREATE TABLE IF NOT EXISTS sub_admins (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        phone VARCHAR(20),
        subAdminOnboardingDate DATE,
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        termOfWork VARCHAR(100),
        status VARCHAR(50) DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create type_of_work table
    await sql`
      CREATE TABLE IF NOT EXISTS type_of_work (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        status VARCHAR(50) DEFAULT 'Active',
        createdDate DATE DEFAULT CURRENT_DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create orders table
    await sql`
      CREATE TABLE IF NOT EXISTS orders (
        id SERIAL PRIMARY KEY,
        orderReferenceNumber VARCHAR(100) UNIQUE NOT NULL,
        orderOnboardingDate DATE,
        client VARCHAR(255),
        typeOfWork VARCHAR(255),
        vendorName VARCHAR(255),
        dateOfWorkCompletionExpected DATE,
        totalInvoiceValue DECIMAL(15,2),
        totalValueGstGovtFees DECIMAL(15,2),
        dateOfPaymentExpected DATE,
        dateOfOnboardingVendor DATE,
        status VARCHAR(100) DEFAULT 'Pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    console.log('✅ Database schema initialized');

    // Seed demo data
    console.log('\n🌱 Seeding demo data...');

    // Seed vendors
    const vendors = [
      {
        company_name: 'TechCorp Solutions',
        company_type: 'Pvt. Company',
        onboarding_date: '2024-01-15',
        emails: ['<EMAIL>'],
        phones: ['+91-**********'],
        address: '123 Tech Street, Bangalore',
        country: 'India',
        state: 'Karnataka',
        city: 'Bangalore',
        username: 'techcorp_admin',
        gst_number: 'GST29ABCDE1234F1Z5',
        description: 'Leading technology solutions provider',
        services: ['Software Development', 'IT Consulting'],
        website: 'https://techcorp.com',
        type_of_work: 'Technology Services',
        status: 'Active',
        rating: 4.8,
        total_orders: 25
      },
      {
        company_name: 'Global Supplies Inc',
        company_type: 'MSME',
        onboarding_date: '2024-02-10',
        emails: ['<EMAIL>'],
        phones: ['+91-**********'],
        address: '456 Supply Chain Road, Mumbai',
        country: 'India',
        state: 'Maharashtra',
        city: 'Mumbai',
        username: 'global_supplies',
        gst_number: 'GST27FGHIJ5678K2L6',
        description: 'Global supply chain management',
        services: ['Supply Chain', 'Logistics'],
        website: 'https://globalsupplies.com',
        type_of_work: 'Supply Chain Management',
        status: 'Active',
        rating: 4.5,
        total_orders: 18
      }
    ];

    for (const vendor of vendors) {
      try {
        await sql`
          INSERT INTO vendors (
            company_name, company_type, onboarding_date, emails, phones,
            address, country, state, city, username, gst_number,
            description, services, website, type_of_work, status,
            rating, total_orders
          ) VALUES (
            ${vendor.company_name}, ${vendor.company_type}, ${vendor.onboarding_date},
            ${JSON.stringify(vendor.emails)}, ${JSON.stringify(vendor.phones)},
            ${vendor.address}, ${vendor.country}, ${vendor.state}, ${vendor.city},
            ${vendor.username}, ${vendor.gst_number}, ${vendor.description},
            ${JSON.stringify(vendor.services)}, ${vendor.website}, ${vendor.type_of_work},
            ${vendor.status}, ${vendor.rating}, ${vendor.total_orders}
          )
          ON CONFLICT (company_name) DO UPDATE SET
            company_type = EXCLUDED.company_type,
            updated_at = CURRENT_TIMESTAMP
        `;
        console.log(`✅ Seeded vendor: ${vendor.company_name}`);
      } catch (error) {
        console.log(`⚠️ Vendor ${vendor.company_name} error:`, error.message);
      }
    }

    // Check data
    console.log('\n📊 Checking seeded data...');
    const vendorCount = await sql`SELECT COUNT(*) as count FROM vendors`;
    const clientCount = await sql`SELECT COUNT(*) as count FROM clients`;
    const orderCount = await sql`SELECT COUNT(*) as count FROM orders`;
    const subAdminCount = await sql`SELECT COUNT(*) as count FROM sub_admins`;
    const workTypeCount = await sql`SELECT COUNT(*) as count FROM type_of_work`;

    console.log(`📈 Data counts:`);
    console.log(`   - Vendors: ${vendorCount[0].count}`);
    console.log(`   - Clients: ${clientCount[0].count}`);
    console.log(`   - Orders: ${orderCount[0].count}`);
    console.log(`   - Sub-admins: ${subAdminCount[0].count}`);
    console.log(`   - Type of work: ${workTypeCount[0].count}`);

    console.log('\n✅ Database test and seeding completed successfully!');

  } catch (error) {
    console.error('\n❌ Error:', error.message);
    console.error('🔍 Error details:', error);
    process.exit(1);
  }
}

testAndSeed();
