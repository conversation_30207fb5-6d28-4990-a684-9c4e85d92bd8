import dotenv from 'dotenv';
import { neon } from '@neondatabase/serverless';

// Load environment variables
dotenv.config();

const DATABASE_URL = process.env.VITE_DATABASE_URL;
const sql = neon(DATABASE_URL);

// Complete demo data sets
const demoVendors = [
  {
    company_name: 'TechCorp Solutions',
    company_type: 'Pvt. Company',
    onboarding_date: '2024-01-15',
    emails: ['<EMAIL>'],
    phones: ['+91-**********'],
    address: '123 Tech Street, Bangalore',
    country: 'India',
    state: 'Karnataka',
    city: 'Bangalore',
    username: 'techcorp_admin',
    gst_number: 'GST29ABCDE1234F1Z5',
    description: 'Leading technology solutions provider',
    services: ['Software Development', 'IT Consulting'],
    website: 'https://techcorp.com',
    type_of_work: 'Technology Services',
    status: 'Active',
    rating: 4.8,
    total_orders: 25
  },
  {
    company_name: 'Global Supplies Inc',
    company_type: 'MSME',
    onboarding_date: '2024-02-10',
    emails: ['<EMAIL>'],
    phones: ['+91-**********'],
    address: '456 Supply Chain Road, Mumbai',
    country: 'India',
    state: 'Maharashtra',
    city: 'Mumbai',
    username: 'global_supplies',
    gst_number: 'GST27FGHIJ5678K2L6',
    description: 'Global supply chain management',
    services: ['Supply Chain', 'Logistics'],
    website: 'https://globalsupplies.com',
    type_of_work: 'Supply Chain Management',
    status: 'Active',
    rating: 4.5,
    total_orders: 18
  },
  {
    company_name: 'Creative Design Studio',
    company_type: 'Firm',
    onboarding_date: '2024-03-05',
    emails: ['<EMAIL>'],
    phones: ['+91-**********'],
    address: '789 Design Avenue, Delhi',
    country: 'India',
    state: 'Delhi',
    city: 'New Delhi',
    username: 'creative_studio',
    gst_number: 'GST07MNOPQ9012R3S4',
    description: 'Creative design and branding solutions',
    services: ['Graphic Design', 'Branding'],
    website: 'https://creativedesign.com',
    type_of_work: 'Design Services',
    status: 'Active',
    rating: 4.7,
    total_orders: 22
  },
  {
    company_name: 'Marketing Pro Agency',
    company_type: 'Pvt. Company',
    onboarding_date: '2024-04-12',
    emails: ['<EMAIL>'],
    phones: ['+91-9876543213'],
    address: '321 Marketing Street, Pune',
    country: 'India',
    state: 'Maharashtra',
    city: 'Pune',
    username: 'marketing_pro',
    gst_number: 'GST27TUVWX3456Y7Z8',
    description: 'Digital marketing and advertising',
    services: ['Digital Marketing', 'SEO'],
    website: 'https://marketingpro.com',
    type_of_work: 'Digital Marketing',
    status: 'Active',
    rating: 4.6,
    total_orders: 30
  },
  {
    company_name: 'Legal Advisors LLP',
    company_type: 'Individual',
    onboarding_date: '2024-05-20',
    emails: ['<EMAIL>'],
    phones: ['+91-9876543214'],
    address: '654 Law Complex, Chennai',
    country: 'India',
    state: 'Tamil Nadu',
    city: 'Chennai',
    username: 'legal_advisors',
    gst_number: 'GST33ABCDE6789F0G1',
    description: 'Legal consultation and advisory services',
    services: ['Legal Consultation', 'Documentation'],
    website: 'https://legaladvisors.com',
    type_of_work: 'Legal Services',
    status: 'Active',
    rating: 4.9,
    total_orders: 15
  }
];

const demoClients = [
  {
    company_name: 'Acme Corporation',
    company_type: 'Startup',
    onboarding_date: '2024-01-10',
    emails: ['<EMAIL>', '<EMAIL>'],
    phones: ['+91-**********', '+91-**********'],
    address: 'Tech Hub, Bandra Kurla Complex, Mumbai',
    country: 'India',
    state: 'Maharashtra',
    city: 'Mumbai',
    dpiit_registered: false,
    dpiit_number: '',
    status: 'Active'
  },
  {
    company_name: 'Global Tech Inc',
    company_type: 'DPIIT',
    onboarding_date: '2024-02-15',
    emails: ['<EMAIL>'],
    phones: ['+91-9876543220'],
    address: 'Innovation Park, Electronic City, Bangalore',
    country: 'India',
    state: 'Karnataka',
    city: 'Bangalore',
    dpiit_registered: true,
    dpiit_number: 'DPIIT12345',
    status: 'Active'
  },
  {
    company_name: 'StartUp Solutions',
    company_type: 'MSME',
    onboarding_date: '2024-03-20',
    emails: ['<EMAIL>'],
    phones: ['+91-9876543230'],
    address: 'Business District, Gurgaon',
    country: 'India',
    state: 'Haryana',
    city: 'Gurgaon',
    dpiit_registered: false,
    dpiit_number: '',
    status: 'Active'
  },
  {
    company_name: 'Innovative Minds Ltd',
    company_type: 'Small Entity',
    onboarding_date: '2024-04-25',
    emails: ['<EMAIL>'],
    phones: ['+91-9876543240'],
    address: 'Tech Tower, Hyderabad',
    country: 'India',
    state: 'Telangana',
    city: 'Hyderabad',
    dpiit_registered: false,
    dpiit_number: '',
    status: 'Active'
  },
  {
    company_name: 'Enterprise Corp',
    company_type: 'Large Entity',
    onboarding_date: '2024-05-30',
    emails: ['<EMAIL>'],
    phones: ['+91-9876543250'],
    address: 'Corporate Plaza, Noida',
    country: 'India',
    state: 'Uttar Pradesh',
    city: 'Noida',
    dpiit_registered: false,
    dpiit_number: '',
    status: 'Active'
  }
];

const demoSubAdmins = [
  {
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '+91-**********',
    subAdminOnboardingDate: '2024-01-15',
    address: '123 Business Street, Suite 100, Mumbai',
    country: 'India',
    state: 'Maharashtra',
    city: 'Mumbai',
    termOfWork: 'Full-time',
    status: 'Active'
  },
  {
    name: 'Michael Chen',
    email: '<EMAIL>',
    phone: '+91-9876543220',
    subAdminOnboardingDate: '2024-02-20',
    address: '456 Admin Complex, Bangalore',
    country: 'India',
    state: 'Karnataka',
    city: 'Bangalore',
    termOfWork: 'Part-time',
    status: 'Active'
  },
  {
    name: 'Emily Rodriguez',
    email: '<EMAIL>',
    phone: '+91-9876543230',
    subAdminOnboardingDate: '2024-03-10',
    address: '789 Management Tower, Delhi',
    country: 'India',
    state: 'Delhi',
    city: 'New Delhi',
    termOfWork: 'Contract',
    status: 'Active'
  },
  {
    name: 'David Kumar',
    email: '<EMAIL>',
    phone: '+91-9876543240',
    subAdminOnboardingDate: '2024-04-05',
    address: '321 Operations Center, Pune',
    country: 'India',
    state: 'Maharashtra',
    city: 'Pune',
    termOfWork: 'Internship',
    status: 'Active'
  },
  {
    name: 'Lisa Patel',
    email: '<EMAIL>',
    phone: '+91-9876543250',
    subAdminOnboardingDate: '2024-05-15',
    address: '654 Admin Hub, Chennai',
    country: 'India',
    state: 'Tamil Nadu',
    city: 'Chennai',
    termOfWork: 'Full-time',
    status: 'Active'
  }
];

const demoTypeOfWork = [
  {
    name: 'Software Development',
    description: 'Custom software development and programming services',
    status: 'Active',
    createdDate: '2024-01-15'
  },
  {
    name: 'Digital Marketing',
    description: 'SEO, social media marketing, and online advertising',
    status: 'Active',
    createdDate: '2024-02-10'
  },
  {
    name: 'Graphic Design',
    description: 'Logo design, branding, and visual identity services',
    status: 'Active',
    createdDate: '2024-01-20'
  },
  {
    name: 'Content Writing',
    description: 'Blog posts, articles, and copywriting services',
    status: 'Inactive',
    createdDate: '2024-03-05'
  },
  {
    name: 'Data Analysis',
    description: 'Business intelligence and data analytics services',
    status: 'Active',
    createdDate: '2024-02-28'
  }
];

const demoOrders = [
  {
    orderReferenceNumber: 'IS-1703845200000',
    orderOnboardingDate: '2024-06-20',
    client: 'Acme Corporation',
    typeOfWork: 'Software Development',
    dateOfWorkCompletionExpected: '2024-07-20',
    totalInvoiceValue: 245000,
    totalValueGstGovtFees: 44100,
    dateOfPaymentExpected: '2024-07-25',
    dateOfOnboardingVendor: '2024-06-21',
    vendorName: 'TechCorp Solutions',
    status: 'Completed'
  },
  {
    orderReferenceNumber: 'IS-1703845260000',
    orderOnboardingDate: '2024-06-25',
    client: 'Global Tech Inc',
    typeOfWork: 'Digital Marketing',
    dateOfWorkCompletionExpected: '2024-08-25',
    totalInvoiceValue: 35000,
    totalValueGstGovtFees: 6300,
    dateOfPaymentExpected: '2024-08-30',
    dateOfOnboardingVendor: '2024-06-27',
    vendorName: 'Marketing Pro Agency',
    status: 'In Progress'
  },
  {
    orderReferenceNumber: 'IS-1703845320000',
    orderOnboardingDate: '2024-07-01',
    client: 'StartUp Solutions',
    typeOfWork: 'Graphic Design',
    dateOfWorkCompletionExpected: '2024-08-01',
    totalInvoiceValue: 25000,
    totalValueGstGovtFees: 4500,
    dateOfPaymentExpected: '2024-08-05',
    dateOfOnboardingVendor: '2024-07-03',
    vendorName: 'Creative Design Studio',
    status: 'Pending'
  },
  {
    orderReferenceNumber: 'IS-1703845380000',
    orderOnboardingDate: '2024-07-10',
    client: 'Innovative Minds Ltd',
    typeOfWork: 'Content Writing',
    dateOfWorkCompletionExpected: '2024-08-10',
    totalInvoiceValue: 18000,
    totalValueGstGovtFees: 3240,
    dateOfPaymentExpected: '2024-08-15',
    dateOfOnboardingVendor: '2024-07-12',
    vendorName: 'Legal Advisors LLP',
    status: 'In Progress'
  },
  {
    orderReferenceNumber: 'IS-1703845440000',
    orderOnboardingDate: '2024-07-15',
    client: 'Enterprise Corp',
    typeOfWork: 'Data Analysis',
    dateOfWorkCompletionExpected: '2024-09-15',
    totalInvoiceValue: 60000,
    totalValueGstGovtFees: 10800,
    dateOfPaymentExpected: '2024-09-20',
    dateOfOnboardingVendor: '2024-07-17',
    vendorName: 'Global Supplies Inc',
    status: 'Completed'
  }
];

async function seedAllData() {
  try {
    console.log('🚀 Starting comprehensive demo data seeding...');

    // Clear existing demo data first
    console.log('🧹 Clearing existing demo data...');
    await sql`DELETE FROM orders WHERE orderReferenceNumber LIKE 'IS-%'`;
    await sql`DELETE FROM vendors WHERE company_name IN ('TechCorp Solutions', 'Global Supplies Inc', 'Creative Design Studio', 'Marketing Pro Agency', 'Legal Advisors LLP')`;
    await sql`DELETE FROM clients WHERE company_name IN ('Acme Corporation', 'Global Tech Inc', 'StartUp Solutions', 'Innovative Minds Ltd', 'Enterprise Corp')`;
    await sql`DELETE FROM sub_admins WHERE email LIKE '%@innoventory.com'`;
    await sql`DELETE FROM type_of_work WHERE name IN ('Software Development', 'Digital Marketing', 'Graphic Design', 'Content Writing', 'Data Analysis')`;

    // Seed vendors
    console.log('🏢 Seeding vendors...');
    for (const vendor of demoVendors) {
      try {
        await sql`
          INSERT INTO vendors (
            company_name, company_type, onboarding_date, emails, phones,
            address, country, state, city, username, gst_number,
            description, services, website, type_of_work, status,
            rating, total_orders
          ) VALUES (
            ${vendor.company_name}, ${vendor.company_type}, ${vendor.onboarding_date},
            ${JSON.stringify(vendor.emails)}, ${JSON.stringify(vendor.phones)},
            ${vendor.address}, ${vendor.country}, ${vendor.state}, ${vendor.city},
            ${vendor.username}, ${vendor.gst_number}, ${vendor.description},
            ${JSON.stringify(vendor.services)}, ${vendor.website}, ${vendor.type_of_work},
            ${vendor.status}, ${vendor.rating}, ${vendor.total_orders}
          )
        `;
        console.log(`✅ Seeded vendor: ${vendor.company_name}`);
      } catch (error) {
        console.log(`⚠️ Vendor ${vendor.company_name} error:`, error.message);
      }
    }

    // Seed clients
    console.log('👥 Seeding clients...');
    for (const client of demoClients) {
      try {
        await sql`
          INSERT INTO clients (
            company_name, company_type, onboarding_date, emails, phones,
            address, country, state, city, dpiit_registered, dpiit_number, status
          ) VALUES (
            ${client.company_name}, ${client.company_type}, ${client.onboarding_date},
            ${JSON.stringify(client.emails)}, ${JSON.stringify(client.phones)},
            ${client.address}, ${client.country}, ${client.state}, ${client.city},
            ${client.dpiit_registered}, ${client.dpiit_number}, ${client.status}
          )
        `;
        console.log(`✅ Seeded client: ${client.company_name}`);
      } catch (error) {
        console.log(`⚠️ Client ${client.company_name} error:`, error.message);
      }
    }

    // Seed sub-admins
    console.log('👨‍💼 Seeding sub-admins...');
    for (const subAdmin of demoSubAdmins) {
      try {
        await sql`
          INSERT INTO sub_admins (
            name, email, phone, subAdminOnboardingDate, address,
            country, state, city, termOfWork, status
          ) VALUES (
            ${subAdmin.name}, ${subAdmin.email}, ${subAdmin.phone},
            ${subAdmin.subAdminOnboardingDate}, ${subAdmin.address},
            ${subAdmin.country}, ${subAdmin.state}, ${subAdmin.city},
            ${subAdmin.termOfWork}, ${subAdmin.status}
          )
        `;
        console.log(`✅ Seeded sub-admin: ${subAdmin.name}`);
      } catch (error) {
        console.log(`⚠️ Sub-admin ${subAdmin.name} error:`, error.message);
      }
    }

    // Seed type of work
    console.log('🔧 Seeding type of work...');
    for (const work of demoTypeOfWork) {
      try {
        await sql`
          INSERT INTO type_of_work (
            name, description, status, createdDate
          ) VALUES (
            ${work.name}, ${work.description}, ${work.status}, ${work.createdDate}
          )
        `;
        console.log(`✅ Seeded work type: ${work.name}`);
      } catch (error) {
        console.log(`⚠️ Work type ${work.name} error:`, error.message);
      }
    }

    // Seed orders
    console.log('📋 Seeding orders...');
    for (const order of demoOrders) {
      try {
        await sql`
          INSERT INTO orders (
            orderReferenceNumber, orderOnboardingDate, client, typeOfWork,
            dateOfWorkCompletionExpected, totalInvoiceValue, totalValueGstGovtFees,
            dateOfPaymentExpected, dateOfOnboardingVendor, vendorName, status
          ) VALUES (
            ${order.orderReferenceNumber}, ${order.orderOnboardingDate}, ${order.client},
            ${order.typeOfWork}, ${order.dateOfWorkCompletionExpected},
            ${order.totalInvoiceValue}, ${order.totalValueGstGovtFees},
            ${order.dateOfPaymentExpected}, ${order.dateOfOnboardingVendor},
            ${order.vendorName}, ${order.status}
          )
        `;
        console.log(`✅ Seeded order: ${order.orderReferenceNumber}`);
      } catch (error) {
        console.log(`⚠️ Order ${order.orderReferenceNumber} error:`, error.message);
      }
    }

    // Final count
    console.log('\n📊 Final data counts:');
    const vendorCount = await sql`SELECT COUNT(*) as count FROM vendors WHERE company_name IN ('TechCorp Solutions', 'Global Supplies Inc', 'Creative Design Studio', 'Marketing Pro Agency', 'Legal Advisors LLP')`;
    const clientCount = await sql`SELECT COUNT(*) as count FROM clients WHERE company_name IN ('Acme Corporation', 'Global Tech Inc', 'StartUp Solutions', 'Innovative Minds Ltd', 'Enterprise Corp')`;
    const orderCount = await sql`SELECT COUNT(*) as count FROM orders WHERE orderReferenceNumber LIKE 'IS-%'`;
    const subAdminCount = await sql`SELECT COUNT(*) as count FROM sub_admins WHERE email LIKE '%@innoventory.com'`;
    const workTypeCount = await sql`SELECT COUNT(*) as count FROM type_of_work WHERE name IN ('Software Development', 'Digital Marketing', 'Graphic Design', 'Content Writing', 'Data Analysis')`;

    console.log(`   - Demo Vendors: ${vendorCount[0].count}/5`);
    console.log(`   - Demo Clients: ${clientCount[0].count}/5`);
    console.log(`   - Demo Orders: ${orderCount[0].count}/5`);
    console.log(`   - Demo Sub-admins: ${subAdminCount[0].count}/5`);
    console.log(`   - Demo Type of work: ${workTypeCount[0].count}/5`);

    console.log('\n✅ ALL DEMO DATA HAS BEEN SUCCESSFULLY SAVED TO DATABASE!');
    console.log('🎉 Your admin panel now displays data that is fully connected to the database!');

  } catch (error) {
    console.error('❌ Error seeding data:', error);
    process.exit(1);
  }
}

seedAllData();
