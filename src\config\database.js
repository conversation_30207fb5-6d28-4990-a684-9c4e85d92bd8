import { neon } from '@neondatabase/serverless';

// Database connection - get from environment variables
// Support both Vite (import.meta.env) and Node.js (process.env) environments
let DATABASE_URL;

if (typeof import.meta !== 'undefined' && import.meta.env) {
  // Vite/browser environment
  DATABASE_URL = import.meta.env.VITE_DATABASE_URL;
} else {
  // Node.js environment - try both VITE_DATABASE_URL and DATABASE_URL
  DATABASE_URL = process.env.VITE_DATABASE_URL || process.env.DATABASE_URL;
}

if (!DATABASE_URL) {
  throw new Error('Database URL not found. Please set VITE_DATABASE_URL or DATABASE_URL in your .env file.');
}

console.log('🔗 Connecting to database:', DATABASE_URL.replace(/:[^:@]*@/, ':****@')); // Hide password in logs

export const sql = neon(DATABASE_URL);

// Test database connection
export const testConnection = async () => {
  try {
    console.log('🔄 Testing database connection...');
    console.log('🔗 Using database URL:', DATABASE_URL.replace(/:[^:@]*@/, ':****@'));

    const result = await sql`SELECT 1 as test, version() as db_version`;
    console.log('✅ Database connection successful');
    console.log('📊 Database info:', result[0]);
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    console.error('🔍 Error details:', {
      message: error.message,
      code: error.code,
      detail: error.detail
    });

    // Check if it's a common connection issue
    if (error.message.includes('getaddrinfo ENOTFOUND')) {
      console.error('🌐 Network issue: Cannot resolve database hostname');
    } else if (error.message.includes('authentication failed')) {
      console.error('🔐 Authentication issue: Check your database credentials');
    } else if (error.message.includes('database') && error.message.includes('does not exist')) {
      console.error('🗄️ Database issue: Database does not exist');
    }

    return false;
  }
};

// Initialize database (alias for initializeSchema)
export const initializeDatabase = async () => {
  return await initializeSchema();
};

// Initialize database schema
export const initializeSchema = async () => {
  try {
    console.log('🔄 Initializing database schema...');

    // Create vendors table - matches admin panel display fields
    await sql`
      CREATE TABLE IF NOT EXISTS vendors (
        id SERIAL PRIMARY KEY,
        company_name VARCHAR(255) NOT NULL,
        company_type VARCHAR(100),
        onboarding_date DATE,
        onboardingDate DATE, -- camelCase for compatibility
        emails JSONB DEFAULT '[]',
        phones JSONB DEFAULT '[]',
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        username VARCHAR(255),
        gst_number VARCHAR(50),
        description TEXT,
        services JSONB DEFAULT '[]',
        website VARCHAR(255),
        type_of_work VARCHAR(255),
        status VARCHAR(50) DEFAULT 'Active',
        files JSONB DEFAULT '{}',
        rating DECIMAL(3,2) DEFAULT 0.00,
        total_orders INTEGER DEFAULT 0,
        totalOrders INTEGER DEFAULT 0, -- camelCase for compatibility
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create clients table - matches admin panel display fields
    await sql`
      CREATE TABLE IF NOT EXISTS clients (
        id SERIAL PRIMARY KEY,
        company_name VARCHAR(255) NOT NULL,
        company_type VARCHAR(100),
        onboarding_date DATE,
        email VARCHAR(255), -- single email for display
        phone VARCHAR(20), -- single phone for display
        emails JSONB DEFAULT '[]', -- array for storage
        phones JSONB DEFAULT '[]', -- array for storage
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        dpiit_registered BOOLEAN DEFAULT FALSE,
        dpiit_number VARCHAR(100),
        files JSONB DEFAULT '{}',
        status VARCHAR(50) DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create orders table - matches admin panel display fields
    await sql`
      CREATE TABLE IF NOT EXISTS orders (
        id SERIAL PRIMARY KEY,
        orderReferenceNumber VARCHAR(100) UNIQUE NOT NULL, -- matches display field
        order_reference_number VARCHAR(100), -- snake_case for compatibility
        orderOnboardingDate DATE, -- matches display field
        order_onboarding_date DATE, -- snake_case for compatibility
        client VARCHAR(255),
        typeOfWork VARCHAR(255), -- matches display field
        type_of_work VARCHAR(255), -- snake_case for compatibility
        vendorName VARCHAR(255), -- matches display field
        vendor_name VARCHAR(255), -- snake_case for compatibility
        dateOfWorkCompletionExpected DATE, -- matches display field
        date_of_work_completion_expected DATE, -- snake_case for compatibility
        totalInvoiceValue DECIMAL(15,2), -- matches display field
        total_invoice_value DECIMAL(15,2), -- snake_case for compatibility
        totalValueGstGovtFees DECIMAL(15,2),
        total_value_gst_govt_fees DECIMAL(15,2), -- snake_case for compatibility
        dateOfPaymentExpected DATE, -- matches display field
        date_of_payment_expected DATE, -- snake_case for compatibility
        dateOfOnboardingVendor DATE,
        date_of_onboarding_vendor DATE, -- snake_case for compatibility
        currentStatus VARCHAR(100) DEFAULT 'Pending', -- matches display field
        current_status VARCHAR(100) DEFAULT 'Pending', -- snake_case for compatibility
        statusComments TEXT,
        status_comments TEXT, -- snake_case for compatibility
        dateOfStatusChange DATE,
        date_of_status_change DATE, -- snake_case for compatibility
        dateOfWorkCompletionExpectedFromVendor DATE,
        date_of_work_completion_expected_from_vendor DATE, -- snake_case for compatibility
        amountToBePaidToVendor DECIMAL(15,2), -- matches display field
        amount_to_be_paid_to_vendor DECIMAL(15,2), -- snake_case for compatibility
        amountPaidToVendor DECIMAL(15,2) DEFAULT 0, -- matches display field
        amount_paid_to_vendor DECIMAL(15,2) DEFAULT 0, -- snake_case for compatibility
        statusHistory JSONB DEFAULT '[]',
        status_history JSONB DEFAULT '[]', -- snake_case for compatibility
        documentsProvidedByClient JSONB DEFAULT '[]',
        documents_provided_by_client JSONB DEFAULT '[]', -- snake_case for compatibility
        documentsProvidedByVendor JSONB DEFAULT '[]',
        documents_provided_by_vendor JSONB DEFAULT '[]', -- snake_case for compatibility
        invoiceFromVendor JSONB DEFAULT '[]',
        invoice_from_vendor JSONB DEFAULT '[]', -- snake_case for compatibility
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create sub_admins table - matches admin panel display fields
    await sql`
      CREATE TABLE IF NOT EXISTS sub_admins (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        username VARCHAR(255) UNIQUE,
        phone VARCHAR(20),
        subAdminOnboardingDate DATE, -- matches display field
        onboarding_date DATE, -- snake_case for compatibility
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        termOfWork VARCHAR(100), -- matches display field
        term_of_work VARCHAR(100), -- snake_case for compatibility
        role VARCHAR(100),
        department VARCHAR(100),
        pan_number VARCHAR(20),
        panNumber VARCHAR(20), -- camelCase for compatibility
        permissions JSONB DEFAULT '[]',
        files JSONB DEFAULT '{}',
        uploadedFiles JSONB DEFAULT '{}', -- matches form field
        status VARCHAR(50) DEFAULT 'Active',
        last_login TIMESTAMP,
        lastLogin VARCHAR(100), -- formatted string for display
        createdDate DATE, -- matches display field
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create type_of_work table - matches admin panel display fields
    await sql`
      CREATE TABLE IF NOT EXISTS type_of_work (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE, -- matches display field
        description TEXT, -- matches display field
        status VARCHAR(50) DEFAULT 'Active', -- matches display field
        createdDate DATE DEFAULT CURRENT_DATE, -- matches display field
        created_date DATE DEFAULT CURRENT_DATE, -- snake_case for compatibility
        "isActive" BOOLEAN DEFAULT TRUE, -- for backward compatibility
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    console.log('✅ Database schema initialized successfully');
    return true;
  } catch (error) {
    console.error('❌ Database schema initialization failed:', error);
    return false;
  }
};

// Insert sample data using the new seeding service
export const insertSampleData = async () => {
  try {
    console.log('🔄 Inserting sample data using seeding service...');

    // Import the seeding service dynamically to avoid circular dependencies
    const { seedAllData } = await import('../services/seedService.js');

    const result = await seedAllData();

    if (result.success) {
      console.log('✅ Sample data insertion completed successfully');
      console.log('📊 Seeding summary:', result.summary);
      return true;
    } else {
      console.warn('⚠️ Sample data insertion completed with some issues');
      console.log('📊 Seeding summary:', result.summary);
      return true; // Return true to not block app initialization
    }
  } catch (error) {
    console.error('❌ Error inserting sample data:', error);
    return true; // Return true to not block app initialization
  }
};

// Function to refresh database with corrected data
export const refreshDatabaseData = async () => {
  try {
    console.log('🔄 Refreshing database data...');
    console.log('⚠️ Skipping database refresh to avoid schema conflicts');
    console.log('✅ Database refresh completed (skipped for compatibility)');
    return true;
  } catch (error) {
    console.error('❌ Error refreshing database data:', error);
    return false;
  }
};
