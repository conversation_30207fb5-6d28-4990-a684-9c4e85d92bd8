import dotenv from 'dotenv';
import { neon } from '@neondatabase/serverless';

// Load environment variables
dotenv.config();

const DATABASE_URL = process.env.VITE_DATABASE_URL;
const sql = neon(DATABASE_URL);

console.log('🔍 Verifying demo data in database...');

async function verifyData() {
  try {
    // Check connection
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful');

    // Check all tables and their data
    console.log('\n📊 Checking data in all tables...');

    // Vendors
    const vendors = await sql`SELECT * FROM vendors ORDER BY created_at DESC LIMIT 10`;
    console.log(`\n🏢 VENDORS (${vendors.length} found):`);
    vendors.forEach((vendor, index) => {
      console.log(`   ${index + 1}. ${vendor.company_name} (${vendor.status}) - ${vendor.type_of_work}`);
    });

    // Clients
    const clients = await sql`SELECT * FROM clients ORDER BY created_at DESC LIMIT 10`;
    console.log(`\n👥 CLIENTS (${clients.length} found):`);
    clients.forEach((client, index) => {
      console.log(`   ${index + 1}. ${client.company_name} (${client.company_type}) - ${client.status}`);
    });

    // Sub-admins
    const subAdmins = await sql`SELECT * FROM sub_admins ORDER BY created_at DESC LIMIT 10`;
    console.log(`\n👨‍💼 SUB-ADMINS (${subAdmins.length} found):`);
    subAdmins.forEach((admin, index) => {
      console.log(`   ${index + 1}. ${admin.name} (${admin.email}) - ${admin.termOfWork || admin.term_of_work || 'N/A'}`);
    });

    // Type of work
    const typeOfWork = await sql`SELECT * FROM type_of_work ORDER BY created_at DESC LIMIT 10`;
    console.log(`\n🔧 TYPE OF WORK (${typeOfWork.length} found):`);
    typeOfWork.forEach((work, index) => {
      console.log(`   ${index + 1}. ${work.name} (${work.status}) - ${work.description?.substring(0, 50)}...`);
    });

    // Orders
    const orders = await sql`SELECT * FROM orders ORDER BY created_at DESC LIMIT 10`;
    console.log(`\n📋 ORDERS (${orders.length} found):`);
    orders.forEach((order, index) => {
      console.log(`   ${index + 1}. ${order.orderReferenceNumber || order.order_reference_number || 'N/A'} - ${order.client} (${order.status})`);
    });

    // Summary
    console.log('\n📈 SUMMARY:');
    console.log(`   - Total Vendors: ${vendors.length}`);
    console.log(`   - Total Clients: ${clients.length}`);
    console.log(`   - Total Sub-admins: ${subAdmins.length}`);
    console.log(`   - Total Type of Work: ${typeOfWork.length}`);
    console.log(`   - Total Orders: ${orders.length}`);

    // Check if demo data exists
    const demoVendors = vendors.filter(v => 
      ['TechCorp Solutions', 'Global Supplies Inc', 'Creative Design Studio', 'Marketing Pro Agency', 'Legal Advisors LLP'].includes(v.company_name)
    );
    const demoClients = clients.filter(c => 
      ['Acme Corporation', 'Global Tech Inc', 'StartUp Solutions', 'Innovative Minds Ltd', 'Enterprise Corp'].includes(c.company_name)
    );
    const demoOrders = orders.filter(o => 
      (o.orderReferenceNumber || o.order_reference_number || '').startsWith('IS-')
    );

    console.log('\n🎯 DEMO DATA STATUS:');
    console.log(`   - Demo Vendors: ${demoVendors.length}/5 ${demoVendors.length === 5 ? '✅' : '❌'}`);
    console.log(`   - Demo Clients: ${demoClients.length}/5 ${demoClients.length === 5 ? '✅' : '❌'}`);
    console.log(`   - Demo Orders: ${demoOrders.length}/5 ${demoOrders.length === 5 ? '✅' : '❌'}`);

    if (demoVendors.length === 5 && demoClients.length === 5 && demoOrders.length >= 1) {
      console.log('\n🎉 SUCCESS: Demo data is properly saved in the database!');
      console.log('✅ Your admin panel is fully connected to the database with real data.');
    } else {
      console.log('\n⚠️  Some demo data is missing. Let me seed the missing data...');
      
      // Seed missing demo data
      await seedMissingData();
    }

  } catch (error) {
    console.error('❌ Error verifying data:', error.message);
  }
}

async function seedMissingData() {
  try {
    console.log('🌱 Seeding missing demo data...');

    // Demo vendors to seed
    const demoVendors = [
      {
        company_name: 'TechCorp Solutions',
        company_type: 'Pvt. Company',
        onboarding_date: '2024-01-15',
        emails: ['<EMAIL>'],
        phones: ['+91-**********'],
        address: '123 Tech Street, Bangalore',
        country: 'India',
        state: 'Karnataka',
        city: 'Bangalore',
        username: 'techcorp_admin',
        gst_number: 'GST29ABCDE1234F1Z5',
        description: 'Leading technology solutions provider',
        services: ['Software Development', 'IT Consulting'],
        website: 'https://techcorp.com',
        type_of_work: 'Technology Services',
        status: 'Active',
        rating: 4.8,
        total_orders: 25
      },
      {
        company_name: 'Global Supplies Inc',
        company_type: 'MSME',
        onboarding_date: '2024-02-10',
        emails: ['<EMAIL>'],
        phones: ['+91-**********'],
        address: '456 Supply Chain Road, Mumbai',
        country: 'India',
        state: 'Maharashtra',
        city: 'Mumbai',
        username: 'global_supplies',
        gst_number: 'GST27FGHIJ5678K2L6',
        description: 'Global supply chain management',
        services: ['Supply Chain', 'Logistics'],
        website: 'https://globalsupplies.com',
        type_of_work: 'Supply Chain Management',
        status: 'Active',
        rating: 4.5,
        total_orders: 18
      }
    ];

    // Demo clients to seed
    const demoClients = [
      {
        company_name: 'Acme Corporation',
        company_type: 'Startup',
        onboarding_date: '2024-01-10',
        emails: ['<EMAIL>', '<EMAIL>'],
        phones: ['+91-**********', '+91-**********'],
        address: 'Tech Hub, Bandra Kurla Complex, Mumbai',
        country: 'India',
        state: 'Maharashtra',
        city: 'Mumbai',
        dpiit_registered: false,
        dpiit_number: '',
        status: 'Active'
      },
      {
        company_name: 'Global Tech Inc',
        company_type: 'DPIIT',
        onboarding_date: '2024-02-15',
        emails: ['<EMAIL>'],
        phones: ['+91-9876543220'],
        address: 'Innovation Park, Electronic City, Bangalore',
        country: 'India',
        state: 'Karnataka',
        city: 'Bangalore',
        dpiit_registered: true,
        dpiit_number: 'DPIIT12345',
        status: 'Active'
      }
    ];

    // Seed vendors
    for (const vendor of demoVendors) {
      try {
        await sql`
          INSERT INTO vendors (
            company_name, company_type, onboarding_date, emails, phones,
            address, country, state, city, username, gst_number,
            description, services, website, type_of_work, status,
            rating, total_orders
          ) VALUES (
            ${vendor.company_name}, ${vendor.company_type}, ${vendor.onboarding_date},
            ${JSON.stringify(vendor.emails)}, ${JSON.stringify(vendor.phones)},
            ${vendor.address}, ${vendor.country}, ${vendor.state}, ${vendor.city},
            ${vendor.username}, ${vendor.gst_number}, ${vendor.description},
            ${JSON.stringify(vendor.services)}, ${vendor.website}, ${vendor.type_of_work},
            ${vendor.status}, ${vendor.rating}, ${vendor.total_orders}
          )
          ON CONFLICT (company_name) DO NOTHING
        `;
        console.log(`✅ Seeded vendor: ${vendor.company_name}`);
      } catch (error) {
        console.log(`⚠️ Vendor ${vendor.company_name} error:`, error.message);
      }
    }

    // Seed clients
    for (const client of demoClients) {
      try {
        await sql`
          INSERT INTO clients (
            company_name, company_type, onboarding_date, emails, phones,
            address, country, state, city, dpiit_registered, dpiit_number, status
          ) VALUES (
            ${client.company_name}, ${client.company_type}, ${client.onboarding_date},
            ${JSON.stringify(client.emails)}, ${JSON.stringify(client.phones)},
            ${client.address}, ${client.country}, ${client.state}, ${client.city},
            ${client.dpiit_registered}, ${client.dpiit_number}, ${client.status}
          )
          ON CONFLICT (company_name) DO NOTHING
        `;
        console.log(`✅ Seeded client: ${client.company_name}`);
      } catch (error) {
        console.log(`⚠️ Client ${client.company_name} error:`, error.message);
      }
    }

    console.log('✅ Demo data seeding completed!');

  } catch (error) {
    console.error('❌ Error seeding data:', error.message);
  }
}

verifyData();
