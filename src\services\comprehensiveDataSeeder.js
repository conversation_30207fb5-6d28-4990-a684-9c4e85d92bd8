import { sql } from '../config/database.js';

/**
 * Comprehensive Data Seeder - Ensures ALL demo data is saved to database
 * This service will save all demo data that's displayed in admin panel pages
 */

// Demo Vendors Data
const demoVendors = [
  {
    id: 'demo-1',
    company_name: 'TechCorp Solutions',
    company_type: 'Pvt. Company',
    onboarding_date: '2024-01-15',
    emails: ['<EMAIL>'],
    phones: ['+91-**********'],
    address: '123 Tech Street, Bangalore',
    country: 'India',
    state: 'Karnataka',
    city: 'Bangalore',
    username: 'techcorp_admin',
    gst_number: 'GST29ABCDE1234F1Z5',
    description: 'Leading technology solutions provider',
    services: ['Software Development', 'IT Consulting'],
    website: 'https://techcorp.com',
    type_of_work: 'Technology Services',
    status: 'Active',
    rating: 4.8,
    total_orders: 25
  },
  {
    id: 'demo-2',
    company_name: 'Global Supplies Inc',
    company_type: 'MSME',
    onboarding_date: '2024-02-10',
    emails: ['<EMAIL>'],
    phones: ['+91-**********'],
    address: '456 Supply Chain Road, Mumbai',
    country: 'India',
    state: 'Maharashtra',
    city: 'Mumbai',
    username: 'global_supplies',
    gst_number: 'GST27FGHIJ5678K2L6',
    description: 'Global supply chain management',
    services: ['Supply Chain', 'Logistics'],
    website: 'https://globalsupplies.com',
    type_of_work: 'Supply Chain Management',
    status: 'Active',
    rating: 4.5,
    total_orders: 18
  },
  {
    id: 'demo-3',
    company_name: 'Creative Design Studio',
    company_type: 'Firm',
    onboarding_date: '2024-03-05',
    emails: ['<EMAIL>'],
    phones: ['+91-9876543212'],
    address: '789 Design Avenue, Delhi',
    country: 'India',
    state: 'Delhi',
    city: 'New Delhi',
    username: 'creative_studio',
    gst_number: 'GST07MNOPQ9012R3S4',
    description: 'Creative design and branding solutions',
    services: ['Graphic Design', 'Branding'],
    website: 'https://creativedesign.com',
    type_of_work: 'Design Services',
    status: 'Active',
    rating: 4.7,
    total_orders: 22
  },
  {
    id: 'demo-4',
    company_name: 'Marketing Pro Agency',
    company_type: 'Pvt. Company',
    onboarding_date: '2024-04-12',
    emails: ['<EMAIL>'],
    phones: ['+91-9876543213'],
    address: '321 Marketing Street, Pune',
    country: 'India',
    state: 'Maharashtra',
    city: 'Pune',
    username: 'marketing_pro',
    gst_number: 'GST27TUVWX3456Y7Z8',
    description: 'Digital marketing and advertising',
    services: ['Digital Marketing', 'SEO'],
    website: 'https://marketingpro.com',
    type_of_work: 'Digital Marketing',
    status: 'Active',
    rating: 4.6,
    total_orders: 30
  },
  {
    id: 'demo-5',
    company_name: 'Legal Advisors LLP',
    company_type: 'Individual',
    onboarding_date: '2024-05-20',
    emails: ['<EMAIL>'],
    phones: ['+91-9876543214'],
    address: '654 Law Complex, Chennai',
    country: 'India',
    state: 'Tamil Nadu',
    city: 'Chennai',
    username: 'legal_advisors',
    gst_number: 'GST33ABCDE6789F0G1',
    description: 'Legal consultation and advisory services',
    services: ['Legal Consultation', 'Documentation'],
    website: 'https://legaladvisors.com',
    type_of_work: 'Legal Services',
    status: 'Active',
    rating: 4.9,
    total_orders: 15
  }
];

// Demo Clients Data
const demoClients = [
  {
    id: 'demo-client-1',
    onboarding_date: '2024-01-10',
    company_type: 'Startup',
    company_name: 'Acme Corporation',
    emails: ['<EMAIL>', '<EMAIL>'],
    phones: ['+91-**********', '+91-**********'],
    address: 'Tech Hub, Bandra Kurla Complex, Mumbai',
    country: 'India',
    state: 'Maharashtra',
    city: 'Mumbai',
    dpiit_registered: false,
    dpiit_number: '',
    status: 'Active'
  },
  {
    id: 'demo-client-2',
    onboarding_date: '2024-02-15',
    company_type: 'DPIIT',
    company_name: 'Global Tech Inc',
    emails: ['<EMAIL>'],
    phones: ['+91-9876543220'],
    address: 'Innovation Park, Electronic City, Bangalore',
    country: 'India',
    state: 'Karnataka',
    city: 'Bangalore',
    dpiit_registered: true,
    dpiit_number: 'DPIIT12345',
    status: 'Active'
  },
  {
    id: 'demo-client-3',
    onboarding_date: '2024-03-20',
    company_type: 'MSME',
    company_name: 'StartUp Solutions',
    emails: ['<EMAIL>'],
    phones: ['+91-9876543230'],
    address: 'Business District, Gurgaon',
    country: 'India',
    state: 'Haryana',
    city: 'Gurgaon',
    dpiit_registered: false,
    dpiit_number: '',
    status: 'Active'
  },
  {
    id: 'demo-client-4',
    onboarding_date: '2024-04-25',
    company_type: 'Small Entity',
    company_name: 'Innovative Minds Ltd',
    emails: ['<EMAIL>'],
    phones: ['+91-9876543240'],
    address: 'Tech Tower, Hyderabad',
    country: 'India',
    state: 'Telangana',
    city: 'Hyderabad',
    dpiit_registered: false,
    dpiit_number: '',
    status: 'Active'
  },
  {
    id: 'demo-client-5',
    onboarding_date: '2024-05-30',
    company_type: 'Large Entity',
    company_name: 'Enterprise Corp',
    emails: ['<EMAIL>'],
    phones: ['+91-9876543250'],
    address: 'Corporate Plaza, Noida',
    country: 'India',
    state: 'Uttar Pradesh',
    city: 'Noida',
    dpiit_registered: false,
    dpiit_number: '',
    status: 'Active'
  }
];

// Demo Sub-Admins Data
const demoSubAdmins = [
  {
    id: 'demo-sub-1',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '+91-**********',
    subAdminOnboardingDate: '2024-01-15',
    address: '123 Business Street, Suite 100, Mumbai',
    country: 'India',
    state: 'Maharashtra',
    city: 'Mumbai',
    termOfWork: 'Full-time',
    status: 'Active'
  },
  {
    id: 'demo-sub-2',
    name: 'Michael Chen',
    email: '<EMAIL>',
    phone: '+91-9876543220',
    subAdminOnboardingDate: '2024-02-20',
    address: '456 Admin Complex, Bangalore',
    country: 'India',
    state: 'Karnataka',
    city: 'Bangalore',
    termOfWork: 'Part-time',
    status: 'Active'
  },
  {
    id: 'demo-sub-3',
    name: 'Emily Rodriguez',
    email: '<EMAIL>',
    phone: '+91-9876543230',
    subAdminOnboardingDate: '2024-03-10',
    address: '789 Management Tower, Delhi',
    country: 'India',
    state: 'Delhi',
    city: 'New Delhi',
    termOfWork: 'Contract',
    status: 'Active'
  },
  {
    id: 'demo-sub-4',
    name: 'David Kumar',
    email: '<EMAIL>',
    phone: '+91-9876543240',
    subAdminOnboardingDate: '2024-04-05',
    address: '321 Operations Center, Pune',
    country: 'India',
    state: 'Maharashtra',
    city: 'Pune',
    termOfWork: 'Internship',
    status: 'Active'
  },
  {
    id: 'demo-sub-5',
    name: 'Lisa Patel',
    email: '<EMAIL>',
    phone: '+91-9876543250',
    subAdminOnboardingDate: '2024-05-15',
    address: '654 Admin Hub, Chennai',
    country: 'India',
    state: 'Tamil Nadu',
    city: 'Chennai',
    termOfWork: 'Full-time',
    status: 'Active'
  }
];

// Demo Type of Work Data
const demoTypeOfWork = [
  {
    id: 'demo-work-1',
    name: 'Software Development',
    description: 'Custom software development and programming services',
    status: 'Active',
    createdDate: '2024-01-15'
  },
  {
    id: 'demo-work-2',
    name: 'Digital Marketing',
    description: 'SEO, social media marketing, and online advertising',
    status: 'Active',
    createdDate: '2024-02-10'
  },
  {
    id: 'demo-work-3',
    name: 'Graphic Design',
    description: 'Logo design, branding, and visual identity services',
    status: 'Active',
    createdDate: '2024-01-20'
  },
  {
    id: 'demo-work-4',
    name: 'Content Writing',
    description: 'Blog posts, articles, and copywriting services',
    status: 'Inactive',
    createdDate: '2024-03-05'
  },
  {
    id: 'demo-work-5',
    name: 'Data Analysis',
    description: 'Business intelligence and data analytics services',
    status: 'Active',
    createdDate: '2024-02-28'
  }
];

// Demo Orders Data
const demoOrders = [
  {
    id: 'demo-ord-001',
    orderReferenceNumber: 'IS-1703845200000',
    orderOnboardingDate: '2024-06-20',
    client: 'Acme Corporation',
    typeOfWork: 'Software Development',
    dateOfWorkCompletionExpected: '2024-07-20',
    totalInvoiceValue: 245000,
    totalValueGstGovtFees: 44100,
    dateOfPaymentExpected: '2024-07-25',
    dateOfOnboardingVendor: '2024-06-21',
    vendorName: 'TechCorp Solutions',
    status: 'Completed'
  },
  {
    id: 'demo-ord-002',
    orderReferenceNumber: 'IS-1703845260000',
    orderOnboardingDate: '2024-06-25',
    client: 'Global Tech Inc',
    typeOfWork: 'Digital Marketing',
    dateOfWorkCompletionExpected: '2024-08-25',
    totalInvoiceValue: 35000,
    totalValueGstGovtFees: 6300,
    dateOfPaymentExpected: '2024-08-30',
    dateOfOnboardingVendor: '2024-06-27',
    vendorName: 'Marketing Pro Agency',
    status: 'In Progress'
  },
  {
    id: 'demo-ord-003',
    orderReferenceNumber: 'IS-1703845320000',
    orderOnboardingDate: '2024-07-01',
    client: 'StartUp Solutions',
    typeOfWork: 'Graphic Design',
    dateOfWorkCompletionExpected: '2024-08-01',
    totalInvoiceValue: 25000,
    totalValueGstGovtFees: 4500,
    dateOfPaymentExpected: '2024-08-05',
    dateOfOnboardingVendor: '2024-07-03',
    vendorName: 'Creative Design Studio',
    status: 'Pending'
  },
  {
    id: 'demo-ord-004',
    orderReferenceNumber: 'IS-1703845380000',
    orderOnboardingDate: '2024-07-10',
    client: 'Innovative Minds Ltd',
    typeOfWork: 'Content Writing',
    dateOfWorkCompletionExpected: '2024-08-10',
    totalInvoiceValue: 18000,
    totalValueGstGovtFees: 3240,
    dateOfPaymentExpected: '2024-08-15',
    dateOfOnboardingVendor: '2024-07-12',
    vendorName: 'Legal Advisors LLP',
    status: 'In Progress'
  },
  {
    id: 'demo-ord-005',
    orderReferenceNumber: 'IS-1703845440000',
    orderOnboardingDate: '2024-07-15',
    client: 'Enterprise Corp',
    typeOfWork: 'Data Analysis',
    dateOfWorkCompletionExpected: '2024-09-15',
    totalInvoiceValue: 60000,
    totalValueGstGovtFees: 10800,
    dateOfPaymentExpected: '2024-09-20',
    dateOfOnboardingVendor: '2024-07-17',
    vendorName: 'Global Supplies Inc',
    status: 'Completed'
  }
];

/**
 * Seed all demo data to database
 */
export const seedAllDemoData = async () => {
  try {
    console.log('🌱 Starting comprehensive demo data seeding...');

    // Initialize database schema first
    const { initializeSchema } = await import('../config/database.js');
    await initializeSchema();

    // Seed vendors
    await seedVendors();
    
    // Seed clients
    await seedClients();
    
    // Seed sub-admins
    await seedSubAdmins();
    
    // Seed type of work
    await seedTypeOfWork();
    
    // Seed orders
    await seedOrders();

    console.log('✅ All demo data seeded successfully!');
    return true;
  } catch (error) {
    console.error('❌ Error seeding demo data:', error);
    throw error;
  }
};

/**
 * Seed vendors data
 */
const seedVendors = async () => {
  try {
    console.log('🏢 Seeding vendors data...');

    for (const vendor of demoVendors) {
      try {
        await sql`
          INSERT INTO vendors (
            company_name, company_type, onboarding_date, emails, phones,
            address, country, state, city, username, gst_number,
            description, services, website, type_of_work, status,
            rating, total_orders, created_at, updated_at
          ) VALUES (
            ${vendor.company_name}, ${vendor.company_type}, ${vendor.onboarding_date},
            ${JSON.stringify(vendor.emails)}, ${JSON.stringify(vendor.phones)},
            ${vendor.address}, ${vendor.country}, ${vendor.state}, ${vendor.city},
            ${vendor.username}, ${vendor.gst_number}, ${vendor.description},
            ${JSON.stringify(vendor.services)}, ${vendor.website}, ${vendor.type_of_work},
            ${vendor.status}, ${vendor.rating}, ${vendor.total_orders},
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          )
          ON CONFLICT (company_name) DO UPDATE SET
            company_type = EXCLUDED.company_type,
            onboarding_date = EXCLUDED.onboarding_date,
            emails = EXCLUDED.emails,
            phones = EXCLUDED.phones,
            address = EXCLUDED.address,
            country = EXCLUDED.country,
            state = EXCLUDED.state,
            city = EXCLUDED.city,
            username = EXCLUDED.username,
            gst_number = EXCLUDED.gst_number,
            description = EXCLUDED.description,
            services = EXCLUDED.services,
            website = EXCLUDED.website,
            type_of_work = EXCLUDED.type_of_work,
            status = EXCLUDED.status,
            rating = EXCLUDED.rating,
            total_orders = EXCLUDED.total_orders,
            updated_at = CURRENT_TIMESTAMP
        `;
        console.log(`✅ Seeded vendor: ${vendor.company_name}`);
      } catch (error) {
        console.log(`⚠️ Vendor ${vendor.company_name} error:`, error.message);
      }
    }
    console.log('✅ Vendors seeding completed');
  } catch (error) {
    console.error('❌ Error seeding vendors:', error);
    throw error;
  }
};

/**
 * Seed clients data
 */
const seedClients = async () => {
  try {
    console.log('👥 Seeding clients data...');

    for (const client of demoClients) {
      try {
        await sql`
          INSERT INTO clients (
            company_name, company_type, onboarding_date, emails, phones,
            address, country, state, city, dpiit_registered, dpiit_number,
            status, created_at, updated_at
          ) VALUES (
            ${client.company_name}, ${client.company_type}, ${client.onboarding_date},
            ${JSON.stringify(client.emails)}, ${JSON.stringify(client.phones)},
            ${client.address}, ${client.country}, ${client.state}, ${client.city},
            ${client.dpiit_registered}, ${client.dpiit_number}, ${client.status},
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          )
          ON CONFLICT (company_name) DO UPDATE SET
            company_type = EXCLUDED.company_type,
            onboarding_date = EXCLUDED.onboarding_date,
            emails = EXCLUDED.emails,
            phones = EXCLUDED.phones,
            address = EXCLUDED.address,
            country = EXCLUDED.country,
            state = EXCLUDED.state,
            city = EXCLUDED.city,
            dpiit_registered = EXCLUDED.dpiit_registered,
            dpiit_number = EXCLUDED.dpiit_number,
            status = EXCLUDED.status,
            updated_at = CURRENT_TIMESTAMP
        `;
        console.log(`✅ Seeded client: ${client.company_name}`);
      } catch (error) {
        console.log(`⚠️ Client ${client.company_name} error:`, error.message);
      }
    }
    console.log('✅ Clients seeding completed');
  } catch (error) {
    console.error('❌ Error seeding clients:', error);
    throw error;
  }
};

/**
 * Seed sub-admins data
 */
const seedSubAdmins = async () => {
  try {
    console.log('👨‍💼 Seeding sub-admins data...');

    for (const subAdmin of demoSubAdmins) {
      try {
        await sql`
          INSERT INTO sub_admins (
            name, email, phone, subAdminOnboardingDate, address,
            country, state, city, termOfWork, status, created_at, updated_at
          ) VALUES (
            ${subAdmin.name}, ${subAdmin.email}, ${subAdmin.phone},
            ${subAdmin.subAdminOnboardingDate}, ${subAdmin.address},
            ${subAdmin.country}, ${subAdmin.state}, ${subAdmin.city},
            ${subAdmin.termOfWork}, ${subAdmin.status},
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          )
          ON CONFLICT (email) DO UPDATE SET
            name = EXCLUDED.name,
            phone = EXCLUDED.phone,
            subAdminOnboardingDate = EXCLUDED.subAdminOnboardingDate,
            address = EXCLUDED.address,
            country = EXCLUDED.country,
            state = EXCLUDED.state,
            city = EXCLUDED.city,
            termOfWork = EXCLUDED.termOfWork,
            status = EXCLUDED.status,
            updated_at = CURRENT_TIMESTAMP
        `;
        console.log(`✅ Seeded sub-admin: ${subAdmin.name}`);
      } catch (error) {
        console.log(`⚠️ Sub-admin ${subAdmin.name} error:`, error.message);
      }
    }
    console.log('✅ Sub-admins seeding completed');
  } catch (error) {
    console.error('❌ Error seeding sub-admins:', error);
    throw error;
  }
};

/**
 * Seed type of work data
 */
const seedTypeOfWork = async () => {
  try {
    console.log('🔧 Seeding type of work data...');

    for (const work of demoTypeOfWork) {
      try {
        await sql`
          INSERT INTO type_of_work (
            name, description, status, createdDate, "isActive",
            created_at, updated_at
          ) VALUES (
            ${work.name}, ${work.description}, ${work.status}, ${work.createdDate},
            ${work.status === 'Active'}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          )
          ON CONFLICT (name) DO UPDATE SET
            description = EXCLUDED.description,
            status = EXCLUDED.status,
            "isActive" = EXCLUDED."isActive",
            updated_at = CURRENT_TIMESTAMP
        `;
        console.log(`✅ Seeded work type: ${work.name}`);
      } catch (error) {
        console.log(`⚠️ Work type ${work.name} error:`, error.message);
      }
    }
    console.log('✅ Type of work seeding completed');
  } catch (error) {
    console.error('❌ Error seeding type of work:', error);
    throw error;
  }
};

/**
 * Seed orders data
 */
const seedOrders = async () => {
  try {
    console.log('📋 Seeding orders data...');

    for (const order of demoOrders) {
      try {
        await sql`
          INSERT INTO orders (
            orderReferenceNumber, orderOnboardingDate, client, typeOfWork,
            dateOfWorkCompletionExpected, totalInvoiceValue, totalValueGstGovtFees,
            dateOfPaymentExpected, dateOfOnboardingVendor, vendorName, status,
            created_at, updated_at
          ) VALUES (
            ${order.orderReferenceNumber}, ${order.orderOnboardingDate}, ${order.client},
            ${order.typeOfWork}, ${order.dateOfWorkCompletionExpected},
            ${order.totalInvoiceValue}, ${order.totalValueGstGovtFees},
            ${order.dateOfPaymentExpected}, ${order.dateOfOnboardingVendor},
            ${order.vendorName}, ${order.status}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          )
          ON CONFLICT (orderReferenceNumber) DO UPDATE SET
            orderOnboardingDate = EXCLUDED.orderOnboardingDate,
            client = EXCLUDED.client,
            typeOfWork = EXCLUDED.typeOfWork,
            dateOfWorkCompletionExpected = EXCLUDED.dateOfWorkCompletionExpected,
            totalInvoiceValue = EXCLUDED.totalInvoiceValue,
            totalValueGstGovtFees = EXCLUDED.totalValueGstGovtFees,
            dateOfPaymentExpected = EXCLUDED.dateOfPaymentExpected,
            dateOfOnboardingVendor = EXCLUDED.dateOfOnboardingVendor,
            vendorName = EXCLUDED.vendorName,
            status = EXCLUDED.status,
            updated_at = CURRENT_TIMESTAMP
        `;
        console.log(`✅ Seeded order: ${order.orderReferenceNumber}`);
      } catch (error) {
        console.log(`⚠️ Order ${order.orderReferenceNumber} error:`, error.message);
      }
    }
    console.log('✅ Orders seeding completed');
  } catch (error) {
    console.error('❌ Error seeding orders:', error);
    throw error;
  }
};
