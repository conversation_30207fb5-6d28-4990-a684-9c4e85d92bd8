import dotenv from 'dotenv';
import { neon } from '@neondatabase/serverless';

// Load environment variables
dotenv.config();

const DATABASE_URL = process.env.VITE_DATABASE_URL;
const sql = neon(DATABASE_URL);

console.log('🚀 Fixing orders table and seeding data...');

const demoOrders = [
  {
    orderReferenceNumber: 'IS-1703845200000',
    orderOnboardingDate: '2024-06-20',
    client: 'Acme Corporation',
    typeOfWork: 'Software Development',
    dateOfWorkCompletionExpected: '2024-07-20',
    totalInvoiceValue: 245000,
    totalValueGstGovtFees: 44100,
    dateOfPaymentExpected: '2024-07-25',
    dateOfOnboardingVendor: '2024-06-21',
    vendorName: 'TechCorp Solutions',
    currentStatus: 'Completed'
  },
  {
    orderReferenceNumber: 'IS-1703845260000',
    orderOnboardingDate: '2024-06-25',
    client: 'Global Tech Inc',
    typeOfWork: 'Digital Marketing',
    dateOfWorkCompletionExpected: '2024-08-25',
    totalInvoiceValue: 35000,
    totalValueGstGovtFees: 6300,
    dateOfPaymentExpected: '2024-08-30',
    dateOfOnboardingVendor: '2024-06-27',
    vendorName: 'Marketing Pro Agency',
    currentStatus: 'In Progress'
  },
  {
    orderReferenceNumber: 'IS-1703845320000',
    orderOnboardingDate: '2024-07-01',
    client: 'StartUp Solutions',
    typeOfWork: 'Graphic Design',
    dateOfWorkCompletionExpected: '2024-08-01',
    totalInvoiceValue: 25000,
    totalValueGstGovtFees: 4500,
    dateOfPaymentExpected: '2024-08-05',
    dateOfOnboardingVendor: '2024-07-03',
    vendorName: 'Creative Design Studio',
    currentStatus: 'Pending'
  },
  {
    orderReferenceNumber: 'IS-1703845380000',
    orderOnboardingDate: '2024-07-10',
    client: 'Innovative Minds Ltd',
    typeOfWork: 'Content Writing',
    dateOfWorkCompletionExpected: '2024-08-10',
    totalInvoiceValue: 18000,
    totalValueGstGovtFees: 3240,
    dateOfPaymentExpected: '2024-08-15',
    dateOfOnboardingVendor: '2024-07-12',
    vendorName: 'Legal Advisors LLP',
    currentStatus: 'In Progress'
  },
  {
    orderReferenceNumber: 'IS-1703845440000',
    orderOnboardingDate: '2024-07-15',
    client: 'Enterprise Corp',
    typeOfWork: 'Data Analysis',
    dateOfWorkCompletionExpected: '2024-09-15',
    totalInvoiceValue: 60000,
    totalValueGstGovtFees: 10800,
    dateOfPaymentExpected: '2024-09-20',
    dateOfOnboardingVendor: '2024-07-17',
    vendorName: 'Global Supplies Inc',
    currentStatus: 'Completed'
  }
];

async function fixAndSeed() {
  try {
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful');

    // Check current orders table schema
    console.log('\n🔍 Checking orders table schema...');
    try {
      const schema = await sql`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'orders' 
        ORDER BY ordinal_position
      `;
      console.log('📋 Current orders table columns:');
      schema.forEach(col => {
        console.log(`   - ${col.column_name}: ${col.data_type}`);
      });
    } catch (error) {
      console.log('⚠️ Could not check schema:', error.message);
    }

    // Add missing status column if it doesn't exist
    console.log('\n🔧 Adding missing status column...');
    try {
      await sql`ALTER TABLE orders ADD COLUMN IF NOT EXISTS status VARCHAR(100) DEFAULT 'Pending'`;
      console.log('✅ Status column added/verified');
    } catch (error) {
      console.log('⚠️ Status column issue:', error.message);
    }

    // Seed orders using the correct column names
    console.log('\n📋 Seeding demo orders...');
    for (const order of demoOrders) {
      try {
        // Try with currentStatus first (which exists in the schema)
        await sql`
          INSERT INTO orders (
            orderReferenceNumber, orderOnboardingDate, client, typeOfWork,
            dateOfWorkCompletionExpected, totalInvoiceValue, totalValueGstGovtFees,
            dateOfPaymentExpected, dateOfOnboardingVendor, vendorName, currentStatus
          ) VALUES (
            ${order.orderReferenceNumber}, ${order.orderOnboardingDate}, ${order.client},
            ${order.typeOfWork}, ${order.dateOfWorkCompletionExpected},
            ${order.totalInvoiceValue}, ${order.totalValueGstGovtFees},
            ${order.dateOfPaymentExpected}, ${order.dateOfOnboardingVendor},
            ${order.vendorName}, ${order.currentStatus}
          )
        `;
        console.log(`✅ Seeded order: ${order.orderReferenceNumber}`);
      } catch (error) {
        if (error.message.includes('duplicate key')) {
          console.log(`⚠️ Order ${order.orderReferenceNumber} already exists`);
        } else {
          console.log(`⚠️ Order ${order.orderReferenceNumber} error:`, error.message);
          
          // Try alternative approach without status
          try {
            await sql`
              INSERT INTO orders (
                orderReferenceNumber, orderOnboardingDate, client, typeOfWork,
                dateOfWorkCompletionExpected, totalInvoiceValue, totalValueGstGovtFees,
                dateOfPaymentExpected, dateOfOnboardingVendor, vendorName
              ) VALUES (
                ${order.orderReferenceNumber}, ${order.orderOnboardingDate}, ${order.client},
                ${order.typeOfWork}, ${order.dateOfWorkCompletionExpected},
                ${order.totalInvoiceValue}, ${order.totalValueGstGovtFees},
                ${order.dateOfPaymentExpected}, ${order.dateOfOnboardingVendor},
                ${order.vendorName}
              )
            `;
            console.log(`✅ Seeded order (without status): ${order.orderReferenceNumber}`);
          } catch (altError) {
            console.log(`❌ Alternative insert failed for ${order.orderReferenceNumber}:`, altError.message);
          }
        }
      }
    }

    // Final verification
    console.log('\n📊 Final verification...');
    const vendorCount = await sql`SELECT COUNT(*) as count FROM vendors WHERE company_name IN ('TechCorp Solutions', 'Global Supplies Inc', 'Creative Design Studio', 'Marketing Pro Agency', 'Legal Advisors LLP')`;
    const clientCount = await sql`SELECT COUNT(*) as count FROM clients WHERE company_name IN ('Acme Corporation', 'Global Tech Inc', 'StartUp Solutions', 'Innovative Minds Ltd', 'Enterprise Corp')`;
    const orderCount = await sql`SELECT COUNT(*) as count FROM orders WHERE orderReferenceNumber LIKE 'IS-%'`;
    const subAdminCount = await sql`SELECT COUNT(*) as count FROM sub_admins WHERE email LIKE '%@innoventory.com'`;
    const workTypeCount = await sql`SELECT COUNT(*) as count FROM type_of_work WHERE name IN ('Software Development', 'Digital Marketing', 'Graphic Design', 'Content Writing', 'Data Analysis')`;

    console.log('📈 FINAL DATA COUNTS:');
    console.log(`   - Demo Vendors: ${vendorCount[0].count}/5 ${vendorCount[0].count === 5 ? '✅' : '❌'}`);
    console.log(`   - Demo Clients: ${clientCount[0].count}/5 ${clientCount[0].count >= 5 ? '✅' : '❌'}`);
    console.log(`   - Demo Orders: ${orderCount[0].count}/5 ${orderCount[0].count >= 5 ? '✅' : '❌'}`);
    console.log(`   - Demo Sub-admins: ${subAdminCount[0].count}/5 ${subAdminCount[0].count === 5 ? '✅' : '❌'}`);
    console.log(`   - Demo Type of work: ${workTypeCount[0].count}/5 ${workTypeCount[0].count === 5 ? '✅' : '❌'}`);

    if (vendorCount[0].count === 5 && clientCount[0].count >= 5 && orderCount[0].count >= 5 && subAdminCount[0].count === 5 && workTypeCount[0].count === 5) {
      console.log('\n🎉 SUCCESS! ALL DEMO DATA IS NOW SAVED IN THE DATABASE!');
      console.log('✅ Your admin panel is fully connected to the database with real data.');
      console.log('🚀 All data displayed in the admin panel pages is now saved and persistent.');
      console.log('\n📋 Summary:');
      console.log('   - Vendors: All 5 demo vendors are saved');
      console.log('   - Clients: All 5+ demo clients are saved');
      console.log('   - Orders: All 5+ demo orders are saved');
      console.log('   - Sub-admins: All 5 demo sub-admins are saved');
      console.log('   - Type of work: All 5 demo work types are saved');
    } else {
      console.log('\n⚠️ Some data is still missing. Please check the errors above.');
    }

    // Show sample data
    console.log('\n📋 Sample orders in database:');
    const sampleOrders = await sql`SELECT orderReferenceNumber, client, vendorName, currentStatus FROM orders WHERE orderReferenceNumber LIKE 'IS-%' LIMIT 3`;
    sampleOrders.forEach((order, index) => {
      console.log(`   ${index + 1}. ${order.orderreferencenumber} - ${order.client} → ${order.vendorname} (${order.currentstatus || 'Pending'})`);
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

fixAndSeed();
