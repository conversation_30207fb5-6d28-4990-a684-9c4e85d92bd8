import dotenv from 'dotenv';
import { seedAllDemoData } from './src/services/comprehensiveDataSeeder.js';

// Load environment variables
dotenv.config();

console.log('🚀 Starting comprehensive demo data seeding...');
console.log('📍 Database URL:', process.env.VITE_DATABASE_URL ? process.env.VITE_DATABASE_URL.replace(/:[^:@]*@/, ':****@') : 'NOT SET');

if (!process.env.VITE_DATABASE_URL) {
  console.error('❌ VITE_DATABASE_URL is not set in environment variables');
  process.exit(1);
}

async function main() {
  try {
    console.log('🌱 Seeding all demo data to database...\n');
    
    await seedAllDemoData();
    
    console.log('\n✅ All demo data has been successfully seeded to the database!');
    console.log('📊 The following data has been saved:');
    console.log('   - 5 Vendors');
    console.log('   - 5 Clients');
    console.log('   - 5 Sub-Admins');
    console.log('   - 5 Type of Work entries');
    console.log('   - 5 Orders');
    console.log('\n🎉 Your admin panel is now fully connected to the database with real data!');
    
  } catch (error) {
    console.error('\n❌ Error seeding demo data:', error.message);
    console.error('🔍 Error details:', error);
    process.exit(1);
  }
}

main();
